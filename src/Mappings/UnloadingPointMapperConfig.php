<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\UnloadingPointDto;
use App\Entity\Main\UnloadingPoint;
use App\Services\AccessHelper;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class UnloadingPointMapperConfig implements AutoMapperConfiguratorInterface
{
    public function __construct(protected AccessHelper $accessHelper)
    {
    }

    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(UnloadingPoint::class, UnloadingPointDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('status', fn ($entity): string => 'Aktiv')
            ->forMember('dsdId', fn ($entity) => $entity->getDsdId())
            ->forMember('esaId', fn ($entity) => $entity->getUnloadingPointId())
            ->forMember('name', fn ($entity): string => $entity->getName1().' '.$entity->getName2())
            ->forMember('street', fn ($entity) => $entity->getStreet())
            ->forMember('houseNumber', fn ($entity) => $entity->getHouseNumber())
            ->forMember('postalCode', fn ($entity) => $entity->getPostalCode())
            ->forMember('city', fn ($entity) => $entity->getCity())
            ->forMember('district', fn ($entity) => $entity->getDistrict())
            ->forMember('detailLink', fn ($entity, $mapper, $context) => $entity->getUuid() ? $context['generateUrl']('management_unloadingpoint_details', ['uuid' => $entity->getUuid()]) : null)
            ->forMember('defaultDailyMaxContingent', fn ($entity) => $entity->getDefaultDailyMaxContingent())
            ->forMember('defaultWeeklyMaxContingent', fn ($entity) => $entity->getDefaultWeeklyMaxContingent())
            ->forMember('deviantDailyMaxContingent', fn ($entity) => $entity->getDeviantDailyMaxContingent())
            ->forMember('deviantWeeklyMaxContingent', fn ($entity) => $entity->getDeviantWeeklyMaxContingent())
            ->forMember('deviantContingentValidFrom', fn ($entity) => $entity->getDeviantContingentValidFrom()->format('d.m.Y'))
            ->forMember('deviantContingentValidTo', fn ($entity) => $entity->getDeviantContingentValidTo()->format('d.m.Y'))
        ;

        $config->registerMapping(UnloadingPointDto::class, UnloadingPoint::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('uuid', Operation::fromProperty(propertyName: 'id'))
            ->forMember('dsdId', Operation::fromProperty(propertyName: 'dsdId'))
            ->forMember('name', Operation::fromProperty(propertyName: 'name1'))
            ->forMember('street', Operation::fromProperty(propertyName: 'street'))
            ->forMember('houseNumber', Operation::fromProperty(propertyName: 'houseNumber'))
            ->forMember('postalCode', Operation::fromProperty(propertyName: 'postalCode'))
            ->forMember('city', Operation::fromProperty(propertyName: 'city'))
            ->forMember('district', Operation::fromProperty(propertyName: 'district'))
            ->forMember('defaultDailyMaxContingent', Operation::fromProperty(propertyName: 'defaultDailyMaxContingent'))
            ->forMember('defaultWeeklyMaxContingent', Operation::fromProperty(propertyName: 'defaultWeeklyMaxContingent'))
            ->forMember('deviantDailyMaxContingent', Operation::fromProperty(propertyName: 'deviantDailyMaxContingent'))
            ->forMember('deviantWeeklyMaxContingent', Operation::fromProperty(propertyName: 'deviantWeeklyMaxContingent'))
            ->forMember('deviantContingentValidFrom', Operation::fromProperty(propertyName: 'deviantContingentValidFrom'))
            ->forMember('deviantContingentValidTo', Operation::fromProperty(propertyName: 'deviantContingentValidTo'))
        ;
    }
}
