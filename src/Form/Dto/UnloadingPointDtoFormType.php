<?php

declare(strict_types=1);

namespace App\Form\Dto;

use App\Dto\UnloadingPointDto;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UnloadingPointDtoFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('deviantContingentValidFrom', DateTimeType::class, [
                'required' => false,
                'label' => 'Abweichendes Kontingent Gültig von',
                'attr' => [
                    'placeholder' => 'dd.mm.yyyy',
                ],
            ])
            ->add('deviantContingentValidTo', DateTimeType::class, [
                'required' => false,
                'label' => 'Abweichendes Kontingent Gültig bis',
                'attr' => [
                    'placeholder' => 'dd.mm.yyyy',
                ],
            ])
            ->add('deviantDailyMaxContingent', IntegerType::class, [
                'required' => false,
                'label' => 'Abweichendes Tageskontingent',
            ])
            ->add('deviantWeeklyMaxContingent', IntegerType::class, [
                'required' => false,
                'label' => 'Abweichendes Wochenkontingent',
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(defaults: [
            'mode' => 'view',
            'data_class' => UnloadingPointDto::class,
        ]);
    }
}
