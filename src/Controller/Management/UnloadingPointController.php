<?php

declare(strict_types=1);

namespace App\Controller\Management;

use App\Dto\ContractAreaDto;
use App\Dto\UnloadingPointDto;
use App\Dto\UserDto;
use App\Entity\Main\ContractArea;
use App\Entity\Main\UnloadingPoint;
use App\Entity\Main\User;
use App\Form\Dto\UnloadingPointDtoFormType;
use App\Form\Dto\UserDtoFormType;
use App\Repository\Main\ContractAreaRepository;
use App\Repository\Main\UnloadingPointRepository;
use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use App\Services\ContractAreaHelper;
use App\Services\UnloadingPointHelper;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends BaseCrudController<UnloadingPoint>
 */
class UnloadingPointController extends BaseCrudController
{
    protected SessionInterface $session;

    public function __construct(
        protected EntityManagerInterface $manager,
        RequestStack $requestStack,
        protected AccessHelper $accessHelper,
        protected ContractAreaHelper $contractAreaHelper,
        CollectingPlaceHelper $collectingPlaceHelper,
        UnloadingPointHelper $unloadingPointHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        UnloadingPointRepository $repository,
        private readonly TranslatorInterface $translator,
        private readonly ContractAreaRepository $contractAreaRepository,
    ) {
        parent::__construct(repository: $repository, serializer: $serializer, entityManager: $manager, accessHelper: $accessHelper, contractAreaHelper: $contractAreaHelper, collectingPlaceHelper: $collectingPlaceHelper, unloadingPointHelper: $unloadingPointHelper, controllerName: 'UnloadingPointController', listTwig: 'management-neu/unloadingpoint/list.html.twig', detailTwig: 'management-neu/unloadingpoint/details.html.twig');
        $this->session = $requestStack->getSession();
    }

    protected function mapEntityToDto(object $entity, string|Request $request, ?object $unloadingPoint = null, ?object $collectingPlace = null): object
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, $request, [
            'unloadingPoint' => $unloadingPoint,
            'collectingPlace' => $collectingPlace,
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity(object $dto, object $entity, Request $request): object
    {
        /** @phpstan-ignore-next-line */
        return $this->autoMapper->mapToObject($dto, $entity, []);
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getFormOptions(object $dto, string $mode, array $options): array
    {
        switch ($mode) {
            case BaseCrudController::FORM_MODE_EDIT:
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_EDIT, 'method' => Request::METHOD_PUT]);

                break;
        }

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getListViewOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            // 'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'dsdId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.dsdid')],
            'esaId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.esaid')],
            'name' => ['visible' => true, 'label' => $this->translator->trans('name')],
            'street' => ['visible' => true, 'label' => $this->translator->trans('street')],
            'houseNumber' => ['visible' => true, 'label' => $this->translator->trans('house_number')],
            'postalCode' => ['visible' => true, 'label' => $this->translator->trans('postal_code')],
            'city' => ['visible' => true, 'label' => $this->translator->trans('city')],
            'district' => ['visible' => true, 'label' => $this->translator->trans('district')],
            'detailLink' => ['visible' => false, 'label' => 'detailLink'],
        ];

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getContractAreaOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => 'Status'],
            'name' => ['visible' => true, 'label' => 'Name'],
            'validFrom' => ['visible' => true, 'label' => 'Gültig von'],
            'validTo' => ['visible' => true, 'label' => 'Gültig bis'],
        ];

        return $options;
    }

    #[Route(path: '/management/unloadingPoint', name: 'management_unloadingpoint')]
    public function index(): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        $self = $this;
        $items = $this->manager->getRepository(UnloadingPoint::class)->findAll();
        $mappedList = array_map(callback: fn (UnloadingPoint $entity): object => $self->mapEntityToDto(entity: $entity, request: UnloadingPointDto::class), array: $items);

        return $this->render(view: 'management-neu/unloadingpoint/list.html.twig', parameters: $this->getListViewOptions(entityList: $items, options: [
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/unloadingPoint/{uuid}', name: 'management_unloadingpoint_details')]
    public function details(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->redirectToRoute(route: 'management_unloadingPoint_details_contractarea', parameters: ['uuid' => $uuid]);
    }

    #[Route(path: '/management/unloadingPoint/{uuid}/contractArea', name: 'management_unloadingPoint_details_contractarea')]
    public function detailsContingents(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $unloadingPoint = $this->manager->getRepository(UnloadingPoint::class)->findOneBy(criteria: ['uuid' => $uuid]);

        $editDto = $this->mapEntityToDto(entity: $unloadingPoint, request: UnloadingPointDto::class);
        $editForm = $this->createForm(type: UnloadingPointDtoFormType::class, data: $editDto, options: $this->getFormOptions(dto: $editDto, mode: BaseCrudController::FORM_MODE_EDIT, options: []));
        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
        }

        $items = $this->contractAreaRepository->getContractAreaByUnloadingPoint(unloadingPoint: $unloadingPoint);

        $mappedList = array_map(callback: fn (ContractArea $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractAreaDto::class, unloadingPoint: $unloadingPoint), array: $items);

        return $this->render(view: 'management-neu/unloadingpoint/details.html.twig', parameters: $this->getContractAreaOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_unloadingpoint'),
            'unloadingPoint' => $unloadingPoint,
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
            'editForm' => $editForm->createView(),
        ]));
    }
}
